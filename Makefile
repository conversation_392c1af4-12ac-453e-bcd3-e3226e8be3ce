# RepoSense AI - Simplified Makefile
# Unified Docker approach - no more complex build/deployment scripts

.PHONY: help start stop restart logs dev build clean status config health

# Default target
help:
	@echo "RepoSense AI - Simplified Commands"
	@echo "=================================="
	@echo ""
	@echo "🚀 Quick Start:"
	@echo "  start         - Start RepoSense AI (docker-compose up -d)"
	@echo "  stop          - Stop RepoSense AI (docker-compose down)"
	@echo "  restart       - Restart RepoSense AI"
	@echo "  logs          - View application logs"
	@echo ""
	@echo "🔧 Development:"
	@echo "  dev           - Start in development mode (with debug)"
	@echo "  build         - Build Docker image"
	@echo "  clean         - Clean Docker resources"
	@echo ""
	@echo "📋 Maintenance:"
	@echo "  status        - Check service status"
	@echo "  config        - Show configuration summary"
	@echo "  health        - Check application health"
	@echo ""
	@echo "🌐 Access: http://localhost:5000"

# Quick Start Commands
start:
	@echo "🚀 Starting RepoSense AI..."
	docker-compose up -d
	@echo "✅ RepoSense AI started!"
	@echo "🌐 Access: http://localhost:5000"

stop:
	@echo "🛑 Stopping RepoSense AI..."
	docker-compose down
	@echo "✅ RepoSense AI stopped"

restart: stop start
	@echo "🔄 RepoSense AI restarted"

logs:
	@echo "📋 Viewing application logs..."
	docker-compose logs -f reposense-ai

# Development Commands
dev:
	@echo "🔧 Starting RepoSense AI in development mode..."
	@echo "Creating .env with debug settings..."
	@echo "FLASK_DEBUG=1" > .env
	@echo "REPOSENSE_AI_LOG_LEVEL=DEBUG" >> .env
	@echo "PYTHONUNBUFFERED=1" >> .env
	docker-compose up -d
	@echo "✅ Development mode started!"
	@echo "🌐 Access: http://localhost:5000"

build:
	@echo "🐳 Building Docker image..."
	docker-compose build
	@echo "✅ Docker image built"

clean:
	@echo "🧹 Cleaning Docker resources..."
	docker-compose down -v
	docker system prune -f
	docker volume prune -f
	@echo "✅ Docker resources cleaned"

# Maintenance Commands
status:
	@echo "📊 Service status..."
	docker-compose ps

config:
	@echo "📋 Configuration summary..."
	docker-compose exec reposense-ai python config_summary.py

health:
	@echo "🏥 Health check..."
	@curl -f http://localhost:5000/health || echo "❌ Service not healthy"

# Legacy aliases for compatibility
run: start
deploy: start
