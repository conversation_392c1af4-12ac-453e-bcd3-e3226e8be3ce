{"data_mtime": 1754573834, "dep_lines": [8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 324, 325, 839, 1031, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["logging", "threading", "time", "datetime", "typing", "queue", "dataclasses", "enum", "pathlib", "models", "repository_backends", "document_processor", "document_database", "ollama_client", "tempfile", "os", "json", "re", "builtins", "_frozen_importlib", "_io", "_queue", "_typeshed", "abc", "io", "json.decoder", "repository_backends.base", "types", "typing_extensions"], "hash": "bcde25e03a9be76b11ef5e8d1418696ebd3747d0", "id": "historical_scanner", "ignore_all": true, "interface_hash": "9573329590b5bf923e32328e20315e37585ab7fc", "mtime": 1754609740, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\historical_scanner.py", "plugin_data": null, "size": 50552, "suppressed": [], "version_id": "1.15.0"}