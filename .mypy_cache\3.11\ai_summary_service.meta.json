{"data_mtime": 1754614397, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 13, 14, 216, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 10, 5, 5, 5, 20, 5, 30, 30, 30], "dependencies": ["logging", "json", "asyncio", "typing", "datetime", "requests", "pathlib", "document_database", "config_manager", "time", "builtins", "_frozen_importlib", "abc", "types"], "hash": "ec0b6d59a104648ab18d6e7849ee4ce6e5ae4364", "id": "ai_summary_service", "ignore_all": true, "interface_hash": "7c85e20f9cec52a690ddfeaafb428240bd05814e", "mtime": 1754614353, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\ai_summary_service.py", "plugin_data": null, "size": 8555, "suppressed": [], "version_id": "1.15.0"}