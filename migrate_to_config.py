#!/usr/bin/env python3
"""
RepoSense AI Configuration Migration Tool

This script helps migrate from environment variable-based configuration
to the new web interface-based configuration approach.

Usage:
    python migrate_to_config.py
"""

import json
import os
import sys
from pathlib import Path

def get_current_env_config():
    """Extract current configuration from environment variables"""
    env_config = {}
    
    # Ollama settings
    if 'OLLAMA_BASE_URL' in os.environ:
        env_config['ollama_host'] = os.environ['OLLAMA_BASE_URL']
    elif 'OLLAMA_HOST' in os.environ:
        env_config['ollama_host'] = os.environ['OLLAMA_HOST']
    
    if 'OLLAMA_MODEL' in os.environ:
        env_config['ollama_model'] = os.environ['OLLAMA_MODEL']
    
    # Web interface settings
    if 'REPOSENSE_AI_WEB_HOST' in os.environ:
        env_config['web_host'] = os.environ['REPOSENSE_AI_WEB_HOST']
    elif 'WEB_HOST' in os.environ:
        env_config['web_host'] = os.environ['WEB_HOST']
    
    if 'REPOSENSE_AI_WEB_PORT' in os.environ:
        try:
            env_config['web_port'] = int(os.environ['REPOSENSE_AI_WEB_PORT'])
        except ValueError:
            pass
    elif 'WEB_PORT' in os.environ:
        try:
            env_config['web_port'] = int(os.environ['WEB_PORT'])
        except ValueError:
            pass
    
    # Email settings
    if 'SMTP_HOST' in os.environ:
        env_config['smtp_host'] = os.environ['SMTP_HOST']
    
    if 'SMTP_PORT' in os.environ:
        try:
            env_config['smtp_port'] = int(os.environ['SMTP_PORT'])
        except ValueError:
            pass
    
    return env_config

def update_config_file(env_config):
    """Update the config.json file with environment values"""
    config_paths = [
        "/app/data/config.json",
        "data/config.json"
    ]
    
    config_path = None
    for path in config_paths:
        if os.path.exists(path):
            config_path = path
            break
    
    if not config_path:
        config_path = "data/config.json"
        # Create data directory if it doesn't exist
        Path(config_path).parent.mkdir(parents=True, exist_ok=True)
    
    # Load existing config or create new one
    config_data = {}
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config_data = json.load(f)
        except Exception as e:
            print(f"⚠️ Error reading existing config: {e}")
            config_data = {}
    
    # Update with environment values
    updated = False
    for key, value in env_config.items():
        if key not in config_data or config_data[key] != value:
            config_data[key] = value
            updated = True
            print(f"✅ Updated {key}: {value}")
    
    if updated:
        # Save updated config
        try:
            with open(config_path, 'w') as f:
                json.dump(config_data, f, indent=2)
            print(f"✅ Configuration saved to {config_path}")
            return True
        except Exception as e:
            print(f"❌ Error saving config: {e}")
            return False
    else:
        print("ℹ️ No updates needed - config file is already up to date")
        return True

def main():
    print("🔄 RepoSense AI Configuration Migration")
    print("=" * 50)
    
    # Get current environment configuration
    env_config = get_current_env_config()
    
    if not env_config:
        print("ℹ️ No environment variables found to migrate")
        print("✅ You can configure RepoSense AI via the web interface at http://localhost:5000")
        return
    
    print("📋 Found environment configuration:")
    for key, value in env_config.items():
        print(f"   {key}: {value}")
    
    print("\n🔄 Migrating to config file...")
    
    if update_config_file(env_config):
        print("\n✅ Migration completed successfully!")
        print("\n📝 Next steps:")
        print("1. Visit http://localhost:5000 to verify your configuration")
        print("2. Remove environment variables from docker-compose.yml or .env")
        print("3. Use the web interface for future configuration changes")
    else:
        print("\n❌ Migration failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
