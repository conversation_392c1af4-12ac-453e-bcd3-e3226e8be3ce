{"data_mtime": 1754499751, "dep_lines": [7, 8, 9, 10, 28, 29, 69, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sys", "os", "time", "pathlib", "monitor_service", "web_interface", "json", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "json.encoder", "types", "typing", "typing_extensions"], "hash": "b55c4932fa246510f3c6a2887281af04cddf4531", "id": "reposense_ai_binary", "ignore_all": false, "interface_hash": "026c659bd50bcc4b17dda32cff37180762b00691", "mtime": 1754528411, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\reposense_ai_binary.py", "plugin_data": null, "size": 3729, "suppressed": [], "version_id": "1.15.0"}