# Docker Compose Override for Development
# This file provides development-specific overrides and ensures proper file permissions
# It's automatically loaded by docker-compose when present

services:
  reposense-ai:
    # Development-specific settings
    environment:
      # Enable debug logging for database operations
      - REPOSENSE_AI_LOG_LEVEL=DEBUG
      - REPOSENSE_AI_DB_DEBUG=true
    
    # Add a command to fix permissions on startup (development only)
    # This runs before the main application starts
    command: >
      bash -c "
        echo '[DEV] Ensuring proper file permissions...' &&
        find /app/data -type f -name '*.db' -exec chmod 644 {} + 2>/dev/null || true &&
        find /app/data -type d -exec chmod 755 {} + 2>/dev/null || true &&
        echo '[DEV] Starting application...' &&
        python start_reposense_ai.py
      "
    
    # Development labels
    labels:
      - "dev.reposense-ai.environment=development"
      - "dev.reposense-ai.permissions-fix=enabled"
