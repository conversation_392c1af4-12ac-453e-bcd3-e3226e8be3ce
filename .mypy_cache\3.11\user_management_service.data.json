{".class": "MypyFile", "_fullname": "user_management_service", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Config": {".class": "SymbolTableNode", "cross_ref": "models.Config", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RepositoryConfig": {".class": "SymbolTableNode", "cross_ref": "models.RepositoryConfig", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "User": {".class": "SymbolTableNode", "cross_ref": "models.User", "kind": "Gdef"}, "UserManagementService": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "user_management_service.UserManagementService", "name": "UserManagementService", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "user_management_service.UserManagementService", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "user_management_service", "mro": ["user_management_service.UserManagementService", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "user_management_service.UserManagementService.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["user_management_service.UserManagementService", "models.Config"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UserManagementService", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assign_user_to_repository": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "user_id", "repo_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "user_management_service.UserManagementService.assign_user_to_repository", "name": "assign_user_to_repository", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "user_id", "repo_id"], "arg_types": ["user_management_service.UserManagementService", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assign_user_to_repository of UserManagementService", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bulk_assign_users": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "user_ids", "repo_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "user_management_service.UserManagementService.bulk_assign_users", "name": "bulk_assign_users", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "user_ids", "repo_id"], "arg_types": ["user_management_service.UserManagementService", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bulk_assign_users of UserManagementService", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "user_management_service.UserManagementService.config", "name": "config", "type": "models.Config"}}, "create_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "username", "email", "full_name", "role", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "user_management_service.UserManagementService.create_user", "name": "create_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "username", "email", "full_name", "role", "kwargs"], "arg_types": ["user_management_service.UserManagementService", "builtins.str", "builtins.str", "builtins.str", "models.UserRole", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_user of UserManagementService", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "UnionType", "items": ["models.User", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "user_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "user_management_service.UserManagementService.delete_user", "name": "delete_user", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "user_id"], "arg_types": ["user_management_service.UserManagementService", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_user of UserManagementService", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_repository_users": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repo_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "user_management_service.UserManagementService.get_repository_users", "name": "get_repository_users", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "repo_id"], "arg_types": ["user_management_service.UserManagementService", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_repository_users of UserManagementService", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["models.User"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user_repositories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "user_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "user_management_service.UserManagementService.get_user_repositories", "name": "get_user_repositories", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "user_id"], "arg_types": ["user_management_service.UserManagementService", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_user_repositories of UserManagementService", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["models.RepositoryConfig"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_users_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "user_management_service.UserManagementService.get_users_summary", "name": "get_users_summary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["user_management_service.UserManagementService"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_users_summary of UserManagementService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "user_management_service.UserManagementService.logger", "name": "logger", "type": "logging.Logger"}}, "subscribe_user_to_repository": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "user_id", "repo_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "user_management_service.UserManagementService.subscribe_user_to_repository", "name": "subscribe_user_to_repository", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "user_id", "repo_id"], "arg_types": ["user_management_service.UserManagementService", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subscribe_user_to_repository of UserManagementService", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unassign_user_from_repository": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "user_id", "repo_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "user_management_service.UserManagementService.unassign_user_from_repository", "name": "unassign_user_from_repository", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "user_id", "repo_id"], "arg_types": ["user_management_service.UserManagementService", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unassign_user_from_repository of UserManagementService", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unsubscribe_user_from_repository": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "user_id", "repo_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "user_management_service.UserManagementService.unsubscribe_user_from_repository", "name": "unsubscribe_user_from_repository", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "user_id", "repo_id"], "arg_types": ["user_management_service.UserManagementService", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsubscribe_user_from_repository of UserManagementService", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "user_id", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "user_management_service.UserManagementService.update_user", "name": "update_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "user_id", "kwargs"], "arg_types": ["user_management_service.UserManagementService", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_user of UserManagementService", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "user_management_service.UserManagementService.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "user_management_service.UserManagementService", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserRole": {".class": "SymbolTableNode", "cross_ref": "models.UserRole", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "user_management_service.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "user_management_service.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "user_management_service.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "user_management_service.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "user_management_service.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "user_management_service.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}}, "path": "C:\\home-repos\\reposense_ai\\user_management_service.py"}