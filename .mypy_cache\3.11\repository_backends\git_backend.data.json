{".class": "MypyFile", "_fullname": "repository_backends.git_backend", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CommitInfo": {".class": "SymbolTableNode", "cross_ref": "models.CommitInfo", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GitBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["repository_backends.base.RepositoryBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "repository_backends.git_backend.GitBackend", "name": "GitBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "repository_backends.git_backend.GitBackend", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "repository_backends.git_backend", "mro": ["repository_backends.git_backend.GitBackend", "repository_backends.base.RepositoryBackend", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.git_backend.GitBackend.__init__", "name": "__init__", "type": null}}, "backend_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "repository_backends.git_backend.GitBackend.backend_type", "name": "backend_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["repository_backends.git_backend.GitBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backend_type of GitBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "repository_backends.git_backend.GitBackend.backend_type", "name": "backend_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["repository_backends.git_backend.GitBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backend_type of GitBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "browse_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "repo_url", "username", "password", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.git_backend.GitBackend.browse_files", "name": "browse_files", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "repo_url", "username", "password", "path"], "arg_types": ["repository_backends.git_backend.GitBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "browse_files of GitBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "discover_repositories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "base_url", "username", "password", "max_depth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.git_backend.GitBackend.discover_repositories", "name": "discover_repositories", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "base_url", "username", "password", "max_depth"], "arg_types": ["repository_backends.git_backend.GitBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "discover_repositories of GitBackend", "ret_type": {".class": "Instance", "args": ["repository_backends.base.RepositoryInfo"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_commit_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "repo", "revision"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.git_backend.GitBackend.get_commit_info", "name": "get_commit_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "repo", "revision"], "arg_types": ["repository_backends.git_backend.GitBackend", "models.RepositoryConfig", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_commit_info of GitBackend", "ret_type": {".class": "UnionType", "items": ["models.CommitInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_diff": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "repo", "revision"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.git_backend.GitBackend.get_diff", "name": "get_diff", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "repo", "revision"], "arg_types": ["repository_backends.git_backend.GitBackend", "models.RepositoryConfig", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_diff of GitBackend", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_latest_revision": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.git_backend.GitBackend.get_latest_revision", "name": "get_latest_revision", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "arg_types": ["repository_backends.git_backend.GitBackend", "models.RepositoryConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_latest_revision of GitBackend", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_repository_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.git_backend.GitBackend.get_repository_info", "name": "get_repository_info", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "arg_types": ["repository_backends.git_backend.GitBackend", "models.RepositoryConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_repository_info of GitBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_discovery": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.git_backend.GitBackend.supports_discovery", "name": "supports_discovery", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["repository_backends.git_backend.GitBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_discovery of GitBackend", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.git_backend.GitBackend.test_connection", "name": "test_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "arg_types": ["repository_backends.git_backend.GitBackend", "models.RepositoryConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test_connection of GitBackend", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate_repository_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.git_backend.GitBackend.validate_repository_config", "name": "validate_repository_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "arg_types": ["repository_backends.git_backend.GitBackend", "models.RepositoryConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_repository_config of GitBackend", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "repository_backends.git_backend.GitBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "repository_backends.git_backend.GitBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RepositoryBackend": {".class": "SymbolTableNode", "cross_ref": "repository_backends.base.RepositoryBackend", "kind": "Gdef"}, "RepositoryConfig": {".class": "SymbolTableNode", "cross_ref": "models.RepositoryConfig", "kind": "Gdef"}, "RepositoryInfo": {".class": "SymbolTableNode", "cross_ref": "repository_backends.base.RepositoryInfo", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "repository_backends.git_backend.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "repository_backends.git_backend.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "repository_backends.git_backend.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "repository_backends.git_backend.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "repository_backends.git_backend.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "repository_backends.git_backend.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "subprocess": {".class": "SymbolTableNode", "cross_ref": "subprocess", "kind": "Gdef"}}, "path": "C:\\home-repos\\reposense_ai\\repository_backends\\git_backend.py"}