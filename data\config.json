{"users": [], "repositories": [{"id": "8f4edaca-d1d9-4299-8b70-7aeef1a40d36", "name": "svn_monitor_server_test_repo", "url": "http://sundc:81/svn/svn_monitor_server_test_repo", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 8, "last_commit_date": "2025-08-06T19:49:43.657025", "last_processed_time": "2025-08-08T19:15:32.935576", "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 8, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "last_scanned_revision": 8, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-08T19:17:14.201810", "total_revisions": null, "processed_revisions": 8, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": []}, {"id": "2ed7cc84-1a77-45e2-b10a-3ff03121188e", "name": "GridSlam", "url": "http://sundc:81/svn/GridSlam", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 12, "last_commit_date": "2014-12-18T03:13:42.055333", "last_processed_time": "2025-08-09T00:04:19.728395", "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 12, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "last_scanned_revision": 12, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-09T00:04:51.341546", "total_revisions": null, "processed_revisions": 12, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": []}, {"id": "df00afb1-4656-46eb-9a42-be34d2455ffc", "name": "HomeAutomationProjects", "url": "http://sundc:81/svn/HomeAutomationProjects", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 20, "last_commit_date": "2023-03-07T11:09:34.265125", "last_processed_time": "2025-08-09T00:09:04.847206", "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 20, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "last_scanned_revision": 20, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-09T00:09:46.055722", "total_revisions": null, "processed_revisions": 20, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": []}, {"id": "7cb5bee1-93bd-472e-9754-3eec0ced46d2", "name": "OpenCogManaged", "url": "http://sundc:81/svn/OpenCogManaged", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 93, "last_commit_date": "2017-10-13T00:01:24.007035", "last_processed_time": "2025-08-09T00:13:49.952988", "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 84, "end_revision": 93, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "last_scanned_revision": 93, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-09T00:11:54.268882", "total_revisions": null, "processed_revisions": 10, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": []}], "ollama_host": "http://************:11434", "ollama_model": "smollm2:latest", "ollama_model_documentation": null, "ollama_model_code_review": null, "ollama_model_risk_assessment": null, "ollama_timeout_base": 180, "ollama_timeout_connection": 30, "ollama_timeout_embeddings": 60, "check_interval": 300, "svn_server_url": "http://sundc:81/svn", "svn_server_username": "fvaneijk", "svn_server_password": "an<PERSON><PERSON><PERSON>", "svn_server_type": "auto", "smtp_host": "localhost", "smtp_port": 587, "smtp_username": null, "smtp_password": null, "email_from": "<EMAIL>", "email_recipients": [], "output_dir": "/app/data/output", "generate_docs": true, "send_emails": false, "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "82f903c649d77c01068f13c50ec49cc60715d42f6ed902d71501da8b63bf6f15", "web_log_entries": 300}