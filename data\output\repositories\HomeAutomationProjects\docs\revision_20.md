## Summary
The commit includes updates to the following sections of the codebase:

1. `TmwPage` class in `pages.go`: New properties have been added, such as `max`, `min`, and `avg`. These are used to display trend data on the page.
2. `Shapes.TmwTrend` struct in `shapes.go`: This struct has been updated with new fields like `max<PERSON><PERSON><PERSON>`, `min<PERSON>abel`, `show<PERSON><PERSON><PERSON>`, etc., to enhance UI interactions.
3. `editordoc.go`: A documentation comment is added for the `TmwPage` class, providing more information about its properties and usage.
4. The overall architecture and structure of the codebase remain unchanged.

## Technical Details
The changes are mainly related to enhancing the user interface (UI) by adding trend data visualization features and improving documentation comments.

The commits introduce new properties in `TmwPage` that will be used to display trend data on the page. The `max`, `min`, and `avg` fields have been added to provide additional information for the user. These properties are likely intended to enhance the overall functionality of the app by making it easier for users to understand the data being presented.

In addition, a new struct called `Shapes.TmwTrend` has been created with updated fields that include visualization options for labeling and grid display. The changes are aimed at enhancing user experience through better UI interactions.

The `editordoc.go` documentation comment provides more information about the `TmwPage` class, which should improve documentation quality overall.

In terms of technical details related to code structure and architecture, these updates do not appear to affect any underlying system functionality or user interface elements. The overall app remains unchanged after this commit series.

Overall, the commits are aimed at enhancing UI interactions by adding trend data visualization features through new properties in `TmwPage`. They also improve documentation quality through more detailed comments on classes and structs.