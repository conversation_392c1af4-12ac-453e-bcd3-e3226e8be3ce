## Summary
Changes:

1. Updated to ESP-IDF 3.0.0 and added a new __ARMCC_VERSION macro in the `common.h` file.

2. Changed all occurrences of `_UINTPTR_TYPE__` to `_uintptr_t`. The former was introduced with Arduino 1.8.x, whereas the latter is part of the C11 standard and should be used for portability across different compilers.

3. Fixed the typo "Esp" in the comment about the new version's source file name (should be "common").

## Technical Details
No specific technical analysis required as these changes are minor and primarily focused on compatibility with newer versions of ESP-IDF.

## Impact Assessment
Changes:

1. No impact on codebase, users, or system functionality. The commit only adds a new header file (`common.h`) that is part of the ESP-IDF 3.0.0 release and includes additional macros for compatibility with older compilers. This does not affect existing code.
2. User-facing features changed: None (according to the documentation).
3. APIs or interfaces modified: None (according to the documentation).
4. Configuration options added/changed: None (according to the documentation).
5. Deployment procedures affected: None (according to the documentation).
6. README, setup guides, or other docs updated: None (according to the documentation).

## Code Review Recommendation
Since these changes are minor and focus on compatibility with newer versions of ESP-IDF, it seems that this commit should not require code review unless there is a specific reason for doing so. The impact on existing code and users is minimal, and no new functionality or features have been introduced. However, if the user has any concerns about these changes or feels that additional testing is required before releasing the updated ESP-IDF build, they can initiate a code review as needed.

## Documentation Impact
No significant impact on documentation since the commit only updates an existing header file (`common.h`) with macros for compatibility. The README and setup guides should still be accurate and up-to-date for this version of ESP-IDF.