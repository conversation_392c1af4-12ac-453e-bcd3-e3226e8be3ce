{"data_mtime": 1754528413, "dep_lines": [7, 8, 9, 10, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["logging", "datetime", "typing", "models", "builtins", "_frozen_importlib", "abc", "enum", "types", "typing_extensions"], "hash": "06b4c7d3892d5238600df12878bff924b199b83f", "id": "user_management_service", "ignore_all": true, "interface_hash": "5452821b30428b306009d81084c600f13b4eec5a", "mtime": 1754348477, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\user_management_service.py", "plugin_data": null, "size": 10680, "suppressed": [], "version_id": "1.15.0"}