{".class": "MypyFile", "_fullname": "historical_scanner", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CommitInfo": {".class": "SymbolTableNode", "cross_ref": "models.CommitInfo", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DocumentDatabase": {".class": "SymbolTableNode", "cross_ref": "document_database.DocumentDatabase", "kind": "Gdef"}, "DocumentProcessor": {".class": "SymbolTableNode", "cross_ref": "document_processor.DocumentProcessor", "kind": "Gdef"}, "DocumentRecord": {".class": "SymbolTableNode", "cross_ref": "document_database.DocumentRecord", "kind": "Gdef"}, "Empty": {".class": "SymbolTableNode", "cross_ref": "_queue.Empty", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "HistoricalScanConfig": {".class": "SymbolTableNode", "cross_ref": "models.HistoricalScanConfig", "kind": "Gdef"}, "HistoricalScanStatus": {".class": "SymbolTableNode", "cross_ref": "models.HistoricalScanStatus", "kind": "Gdef"}, "HistoricalScanner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "historical_scanner.HistoricalScanner", "name": "HistoricalScanner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "historical_scanner", "mro": ["historical_scanner.HistoricalScanner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "backend_manager", "document_processor", "ollama_client", "max_concurrent_scans", "db_path", "config_manager", "monitor_service"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "backend_manager", "document_processor", "ollama_client", "max_concurrent_scans", "db_path", "config_manager", "monitor_service"], "arg_types": ["historical_scanner.HistoricalScanner", "repository_backends.RepositoryBackendManager", "document_processor.DocumentProcessor", "ollama_client.OllamaClient", "builtins.int", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HistoricalScanner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_output_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._check_output_permissions", "name": "_check_output_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "arg_types": ["historical_scanner.HistoricalScanner", "models.RepositoryConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_output_permissions of HistoricalScanner", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_determine_revisions_to_scan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "repo", "scan_config", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._determine_revisions_to_scan", "name": "_determine_revisions_to_scan", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "repo", "scan_config", "backend"], "arg_types": ["historical_scanner.HistoricalScanner", "models.RepositoryConfig", "models.HistoricalScanConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_determine_revisions_to_scan of HistoricalScanner", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_code_review_priority": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._extract_code_review_priority", "name": "_extract_code_review_priority", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["historical_scanner.HistoricalScanner", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_code_review_priority of HistoricalScanner", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_code_review_recommendation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._extract_code_review_recommendation", "name": "_extract_code_review_recommendation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["historical_scanner.HistoricalScanner", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_code_review_recommendation of HistoricalScanner", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_document_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "documentation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._extract_document_metadata", "name": "_extract_document_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "documentation"], "arg_types": ["historical_scanner.HistoricalScanner", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_document_metadata of HistoricalScanner", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_documentation_impact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._extract_documentation_impact", "name": "_extract_documentation_impact", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["historical_scanner.HistoricalScanner", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_documentation_impact of HistoricalScanner", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_metadata_with_llm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "documentation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._extract_metadata_with_llm", "name": "_extract_metadata_with_llm", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "documentation"], "arg_types": ["historical_scanner.HistoricalScanner", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_metadata_with_llm of HistoricalScanner", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_risk_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._extract_risk_level", "name": "_extract_risk_level", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["historical_scanner.HistoricalScanner", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_risk_level of HistoricalScanner", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "section_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._extract_section", "name": "_extract_section", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "section_name"], "arg_types": ["historical_scanner.HistoricalScanner", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_section of HistoricalScanner", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_documentation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "commit_info", "scan_config", "repo_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._generate_documentation", "name": "_generate_documentation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "commit_info", "scan_config", "repo_config"], "arg_types": ["historical_scanner.HistoricalScanner", "models.CommitInfo", "models.HistoricalScanConfig", "models.RepositoryConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_documentation of HistoricalScanner", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_repository_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repo_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._get_repository_config", "name": "_get_repository_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "repo_id"], "arg_types": ["historical_scanner.HistoricalScanner", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_repository_config of HistoricalScanner", "ret_type": {".class": "UnionType", "items": ["models.RepositoryConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_notify_progress_callbacks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "repo_id", "progress"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._notify_progress_callbacks", "name": "_notify_progress_callbacks", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "repo_id", "progress"], "arg_types": ["historical_scanner.HistoricalScanner", "builtins.str", "historical_scanner.ScanProgress"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_notify_progress_callbacks of HistoricalScanner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_revision_batch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "repo_config", "backend", "revisions", "scan_config", "progress"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._process_revision_batch", "name": "_process_revision_batch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "repo_config", "backend", "revisions", "scan_config", "progress"], "arg_types": ["historical_scanner.HistoricalScanner", "models.RepositoryConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "models.HistoricalScanConfig", "historical_scanner.ScanProgress"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_revision_batch of HistoricalScanner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_scan_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._process_scan_task", "name": "_process_scan_task", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "task"], "arg_types": ["historical_scanner.HistoricalScanner", "historical_scanner.ScanTask"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_scan_task of HistoricalScanner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_save_historical_documentation_integrated": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "commit_info", "documentation", "repo_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._save_historical_documentation_integrated", "name": "_save_historical_documentation_integrated", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "commit_info", "documentation", "repo_config"], "arg_types": ["historical_scanner.HistoricalScanner", "models.CommitInfo", "builtins.str", "models.RepositoryConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_save_historical_documentation_integrated of HistoricalScanner", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_estimated_completion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "progress"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._update_estimated_completion", "name": "_update_estimated_completion", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "progress"], "arg_types": ["historical_scanner.HistoricalScanner", "historical_scanner.ScanProgress"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_estimated_completion of HistoricalScanner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_worker_thread": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner._worker_thread", "name": "_worker_thread", "type": null}}, "active_scans": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "historical_scanner.HistoricalScanner.active_scans", "name": "active_scans", "type": {".class": "Instance", "args": ["builtins.str", "historical_scanner.ScanProgress"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "add_progress_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner.add_progress_callback", "name": "add_progress_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "arg_types": ["historical_scanner.HistoricalScanner", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "historical_scanner.ScanProgress"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_progress_callback of HistoricalScanner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "backend_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "historical_scanner.HistoricalScanner.backend_manager", "name": "backend_manager", "type": "repository_backends.RepositoryBackendManager"}}, "cancel_scan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repository_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner.cancel_scan", "name": "cancel_scan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "repository_id"], "arg_types": ["historical_scanner.HistoricalScanner", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancel_scan of HistoricalScanner", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "completed_scans": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "historical_scanner.HistoricalScanner.completed_scans", "name": "completed_scans", "type": {".class": "Instance", "args": ["builtins.str", "historical_scanner.ScanProgress"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "config_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "historical_scanner.HistoricalScanner.config_manager", "name": "config_manager", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "document_db": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "historical_scanner.HistoricalScanner.document_db", "name": "document_db", "type": "document_database.DocumentDatabase"}}, "document_processor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "historical_scanner.HistoricalScanner.document_processor", "name": "document_processor", "type": "document_processor.DocumentProcessor"}}, "get_all_scan_progress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner.get_all_scan_progress", "name": "get_all_scan_progress", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["historical_scanner.HistoricalScanner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all_scan_progress of HistoricalScanner", "ret_type": {".class": "Instance", "args": ["builtins.str", "historical_scanner.ScanProgress"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_scan_progress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repository_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner.get_scan_progress", "name": "get_scan_progress", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "repository_id"], "arg_types": ["historical_scanner.HistoricalScanner", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_scan_progress of HistoricalScanner", "ret_type": {".class": "UnionType", "items": ["historical_scanner.ScanProgress", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_statistics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner.get_statistics", "name": "get_statistics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["historical_scanner.HistoricalScanner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_statistics of HistoricalScanner", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "historical_scanner.HistoricalScanner.logger", "name": "logger", "type": "logging.Logger"}}, "max_concurrent_scans": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "historical_scanner.HistoricalScanner.max_concurrent_scans", "name": "max_concurrent_scans", "type": "builtins.int"}}, "monitor_service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "historical_scanner.HistoricalScanner.monitor_service", "name": "monitor_service", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ollama_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "historical_scanner.HistoricalScanner.ollama_client", "name": "ollama_client", "type": "ollama_client.OllamaClient"}}, "progress_callbacks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "historical_scanner.HistoricalScanner.progress_callbacks", "name": "progress_callbacks", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "historical_scanner.ScanProgress"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "queue_scan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repo", "scan_config", "priority"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner.queue_scan", "name": "queue_scan", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "repo", "scan_config", "priority"], "arg_types": ["historical_scanner.HistoricalScanner", "models.RepositoryConfig", "models.HistoricalScanConfig", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "queue_scan of HistoricalScanner", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_progress_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner.remove_progress_callback", "name": "remove_progress_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "arg_types": ["historical_scanner.HistoricalScanner", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "historical_scanner.ScanProgress"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_progress_callback of HistoricalScanner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "running": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "historical_scanner.HistoricalScanner.running", "name": "running", "type": "builtins.bool"}}, "scan_queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "historical_scanner.HistoricalScanner.scan_queue", "name": "scan_queue", "type": {".class": "Instance", "args": ["historical_scanner.ScanTask"], "extra_attrs": null, "type_ref": "queue.Queue"}}}, "shutdown_event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "historical_scanner.HistoricalScanner.shutdown_event", "name": "shutdown_event", "type": "threading.Event"}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner.start", "name": "start", "type": null}}, "stats": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "historical_scanner.HistoricalScanner.stats", "name": "stats", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.HistoricalScanner.stop", "name": "stop", "type": null}}, "worker_threads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "historical_scanner.HistoricalScanner.worker_threads", "name": "worker_threads", "type": {".class": "Instance", "args": ["threading.Thread"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "historical_scanner.HistoricalScanner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "historical_scanner.HistoricalScanner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "OllamaClient": {".class": "SymbolTableNode", "cross_ref": "ollama_client.OllamaClient", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Queue": {".class": "SymbolTableNode", "cross_ref": "queue.Queue", "kind": "Gdef"}, "RepositoryBackendManager": {".class": "SymbolTableNode", "cross_ref": "repository_backends.RepositoryBackendManager", "kind": "Gdef"}, "RepositoryConfig": {".class": "SymbolTableNode", "cross_ref": "models.RepositoryConfig", "kind": "Gdef"}, "ScanProgress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "historical_scanner.ScanProgress", "name": "ScanProgress", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "historical_scanner.ScanProgress", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 39, "name": "repository_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 40, "name": "total_revisions", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 41, "name": "processed_revisions", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 42, "name": "failed_revisions", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 43, "name": "current_revision", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 44, "name": "started_at", "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 45, "name": "estimated_completion", "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 46, "name": "error_message", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "status", "type": "models.HistoricalScanStatus"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "historical_scanner", "mro": ["historical_scanner.ScanProgress", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "historical_scanner.ScanProgress.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "repository_id", "total_revisions", "processed_revisions", "failed_revisions", "current_revision", "started_at", "estimated_completion", "error_message", "status"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.ScanProgress.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "repository_id", "total_revisions", "processed_revisions", "failed_revisions", "current_revision", "started_at", "estimated_completion", "error_message", "status"], "arg_types": ["historical_scanner.ScanProgress", "builtins.str", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "models.HistoricalScanStatus"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ScanProgress", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "historical_scanner.ScanProgress.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "repository_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_revisions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "processed_revisions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed_revisions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "current_revision"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "started_at"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "estimated_completion"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error_message"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "status"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["repository_id", "total_revisions", "processed_revisions", "failed_revisions", "current_revision", "started_at", "estimated_completion", "error_message", "status"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "historical_scanner.ScanProgress.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["repository_id", "total_revisions", "processed_revisions", "failed_revisions", "current_revision", "started_at", "estimated_completion", "error_message", "status"], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "models.HistoricalScanStatus"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>an<PERSON><PERSON>ress", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "historical_scanner.ScanProgress.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["repository_id", "total_revisions", "processed_revisions", "failed_revisions", "current_revision", "started_at", "estimated_completion", "error_message", "status"], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "models.HistoricalScanStatus"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>an<PERSON><PERSON>ress", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "current_revision": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "historical_scanner.ScanProgress.current_revision", "name": "current_revision", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "error_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "historical_scanner.ScanProgress.error_message", "name": "error_message", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "estimated_completion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "historical_scanner.ScanProgress.estimated_completion", "name": "estimated_completion", "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "failed_revisions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "historical_scanner.ScanProgress.failed_revisions", "name": "failed_revisions", "type": "builtins.int"}}, "processed_revisions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "historical_scanner.ScanProgress.processed_revisions", "name": "processed_revisions", "type": "builtins.int"}}, "repository_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "historical_scanner.ScanProgress.repository_id", "name": "repository_id", "type": "builtins.str"}}, "started_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "historical_scanner.ScanProgress.started_at", "name": "started_at", "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "historical_scanner.ScanProgress.status", "name": "status", "type": "models.HistoricalScanStatus"}}, "total_revisions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "historical_scanner.ScanProgress.total_revisions", "name": "total_revisions", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "historical_scanner.ScanProgress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "historical_scanner.ScanProgress", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ScanTask": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "historical_scanner.ScanTask", "name": "ScanTask", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "historical_scanner.ScanTask", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 28, "name": "repository_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 29, "name": "repository_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 30, "name": "scan_config", "type": "models.HistoricalScanConfig"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "revisions_to_scan", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "created_at", "type": "datetime.datetime"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "priority", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "historical_scanner", "mro": ["historical_scanner.ScanTask", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "historical_scanner.ScanTask.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "repository_id", "repository_name", "scan_config", "revisions_to_scan", "created_at", "priority"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "historical_scanner.ScanTask.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "repository_id", "repository_name", "scan_config", "revisions_to_scan", "created_at", "priority"], "arg_types": ["historical_scanner.ScanTask", "builtins.str", "builtins.str", "models.HistoricalScanConfig", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "datetime.datetime", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ScanTask", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "historical_scanner.ScanTask.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "repository_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "repository_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scan_config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "revisions_to_scan"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "created_at"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "priority"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["repository_id", "repository_name", "scan_config", "revisions_to_scan", "created_at", "priority"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "historical_scanner.ScanTask.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["repository_id", "repository_name", "scan_config", "revisions_to_scan", "created_at", "priority"], "arg_types": ["builtins.str", "builtins.str", "models.HistoricalScanConfig", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "datetime.datetime", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ScanT<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "historical_scanner.ScanTask.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["repository_id", "repository_name", "scan_config", "revisions_to_scan", "created_at", "priority"], "arg_types": ["builtins.str", "builtins.str", "models.HistoricalScanConfig", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "datetime.datetime", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ScanT<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "created_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "historical_scanner.ScanTask.created_at", "name": "created_at", "type": "datetime.datetime"}}, "priority": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "historical_scanner.ScanTask.priority", "name": "priority", "type": "builtins.int"}}, "repository_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "historical_scanner.ScanTask.repository_id", "name": "repository_id", "type": "builtins.str"}}, "repository_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "historical_scanner.ScanTask.repository_name", "name": "repository_name", "type": "builtins.str"}}, "revisions_to_scan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "historical_scanner.ScanTask.revisions_to_scan", "name": "revisions_to_scan", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "scan_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "historical_scanner.ScanTask.scan_config", "name": "scan_config", "type": "models.HistoricalScanConfig"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "historical_scanner.ScanTask.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "historical_scanner.ScanTask", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "historical_scanner.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "historical_scanner.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "historical_scanner.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "historical_scanner.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "historical_scanner.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "historical_scanner.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "C:\\home-repos\\reposense_ai\\historical_scanner.py"}