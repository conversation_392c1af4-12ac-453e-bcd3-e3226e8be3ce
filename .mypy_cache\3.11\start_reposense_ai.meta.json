{"data_mtime": 1754491774, "dep_lines": [12, 13, 14, 15, 24, 50, 157, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sys", "os", "subprocess", "pathlib", "config_summary", "requests", "reposense_ai_app", "builtins", "_frozen_importlib", "_typeshed", "abc", "http", "http.cookiejar", "requests.auth", "requests.exceptions", "requests.models", "typing", "typing_extensions"], "hash": "31b474ccd19d9a6cac6719f91779829ac91181d9", "id": "start_reposense_ai", "ignore_all": false, "interface_hash": "a302d5f1bac2f34eac05291cd10fed98e50e66d2", "mtime": 1754677606, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\start_reposense_ai.py", "plugin_data": null, "size": 6129, "suppressed": [], "version_id": "1.15.0"}