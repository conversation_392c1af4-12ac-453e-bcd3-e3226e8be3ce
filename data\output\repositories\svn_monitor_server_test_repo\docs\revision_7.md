## Summary
The commit includes several changes to improve the mathematical functionality of the Trigonometric Functions Calculator:

1. The `sin_taylor` and `sin_cordic` functions are modified to accept angles in degrees instead of radians, allowing users to choose between these algorithms when calculating sine values.
2. The user interface for comparing the performance of different trigonometric calculations is improved with more informative output messages and a clearer comparison metric.
3. A new command `tan` has been added to calculate tangent using Taylor series and Cordic algorithms, which also includes an error message in case the input angle cannot be converted to radians correctly.
4. The commit provides more detailed instructions on how to run the updated calculator with the improved user interface, ensuring that users can easily find their way through any potential errors or issues that arise during use.
5. A `main` function has been added at the end of the file for testing purposes and as a demonstration of how to use these new functionality features in practice. This allows users to try out the updated calculator by entering commands like "sin 30" or "tan 45".