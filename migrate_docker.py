#!/usr/bin/env python3
"""
Docker Migration Tool for RepoSense AI

This script helps migrate from multiple Dockerfiles to the unified Docker setup.

Usage:
    python migrate_docker.py
"""

import os
import shutil
import sys
from pathlib import Path

def backup_old_files():
    """Backup old Docker files"""
    backup_dir = Path("docker_backup")
    backup_dir.mkdir(exist_ok=True)
    
    old_files = [
        "Dockerfile.dev",
        "Dockerfile.production", 
        "Dockerfile.linux-binary",
        "Dockerfile.prebuilt-binary"
    ]
    
    backed_up = []
    for file in old_files:
        if os.path.exists(file):
            backup_path = backup_dir / file
            shutil.copy2(file, backup_path)
            backed_up.append(file)
            print(f"✅ Backed up {file} to {backup_path}")
    
    return backed_up

def replace_main_dockerfile():
    """Replace main Dockerfile with unified version"""
    if os.path.exists("Dockerfile.unified"):
        if os.path.exists("Dockerfile"):
            shutil.copy2("Dockerfile", "docker_backup/Dockerfile.original")
            print("✅ Backed up original Dockerfile")
        
        shutil.move("Dockerfile.unified", "Dockerfile")
        print("✅ Replaced Dockerfile with unified version")
        return True
    else:
        print("❌ Dockerfile.unified not found")
        return False

def update_docker_ignore():
    """Update .dockerignore for better build performance"""
    dockerignore_content = """# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Node.js
node_modules
npm-debug.log*

# IDEs
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
docker_backup/
*.dockerfile.old

# Development
.env.local
.env.development
*.log
logs/
data/
dist/
build/
"""
    
    with open(".dockerignore", "w") as f:
        f.write(dockerignore_content)
    print("✅ Updated .dockerignore")

def show_usage_instructions():
    """Show instructions for using the new Docker setup"""
    print("\n" + "="*60)
    print("🎉 Docker Migration Complete!")
    print("="*60)
    
    print("\n📋 New Docker Setup:")
    print("   • Single Dockerfile (unified for dev/prod)")
    print("   • docker-compose.yml (production-ready)")
    print("   • docker-compose.override.yml (development)")
    print("   • docker-compose.prod.yml (production overrides)")
    
    print("\n🚀 Usage:")
    print("   Development:")
    print("     docker-compose up -d")
    print("     (automatically uses override file)")
    
    print("\n   Production:")
    print("     docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d")
    
    print("\n   Build specific mode:")
    print("     docker build --build-arg MODE=development -t reposense-ai:dev .")
    print("     docker build --build-arg MODE=production -t reposense-ai:prod .")
    
    print("\n✅ Benefits:")
    print("   • Single Dockerfile to maintain")
    print("   • Automatic dev/prod configuration")
    print("   • Better build caching")
    print("   • Cleaner project structure")
    
    print("\n🗑️  Cleanup (optional):")
    print("   You can now delete the old Docker files from docker_backup/")
    print("   rm -rf docker_backup/")

def main():
    print("🔄 RepoSense AI Docker Migration")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("docker-compose.yml"):
        print("❌ docker-compose.yml not found. Run this script from the project root.")
        sys.exit(1)
    
    # Backup old files
    print("\n📦 Backing up old Docker files...")
    backed_up = backup_old_files()
    
    if not backed_up:
        print("ℹ️ No old Docker files found to backup")
    
    # Replace main Dockerfile
    print("\n🔄 Updating main Dockerfile...")
    if not replace_main_dockerfile():
        print("❌ Failed to update Dockerfile")
        sys.exit(1)
    
    # Update .dockerignore
    print("\n📝 Updating .dockerignore...")
    update_docker_ignore()
    
    # Show usage instructions
    show_usage_instructions()
    
    print("\n🎯 Next Steps:")
    print("1. Test the new setup: docker-compose up -d")
    print("2. Verify web interface: http://localhost:5000")
    print("3. Configure via web interface (no more environment variables!)")

if __name__ == "__main__":
    main()
