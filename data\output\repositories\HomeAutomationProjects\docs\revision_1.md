## Summary
This commit adds support for the VSARDUINO_H header file in a project template. It includes code changes to build and debug with Visual Studio 2015. The author has used tools like Clang and Intellisense to verify that the code compiles, links, and runs correctly on Windows 8, Windows 7, Linux x86_64, and Mac OS X.

## Technical Details
The commit includes the following changes:

1. Created a new project template in VS2015 for an ESP8266 module that supports Visual Studio 2015.
2. Updated the CMakeLists.txt file to include an "add_subdirectory" statement for the new project template.
3. Added #include statements for the vsarduino header file in the main.cpp file.
4. Changed some code from inline functions to function pointers using macros like __builtin_clz and __cxa_pure_virtual.
5. Added a noInterrupts() function that sets all interrupt flags to 0 when the microcontroller enters sleep mode.
6. Used vsarduino::setMode(ESP8266_MODE) in the _Setup() function to set the ESP8266 module to startup mode before starting the main loop().
7. Included a pthread_create example that creates and starts a thread to periodically send data over Bluetooth to an external device.
8. Used a while(true) loop to continuously check for new devices using ESP8266\_CONNECTED events in the _Bluetooth() function.
9. Used printf statements in the _Bluetooth() and _SetLights() functions to debug messages about connected devices and lights.
10. Used a pthread_join example that waits for a thread to finish before continuing with main().

## Impact Assessment
The commit has significant potential impact on users who need to use Visual Studio 2015 to develop projects that target the ESP8266 module. It would enable them to build and debug projects with Visual Studio using a project template that is compatible with VS2015. This could simplify the development process for users of this module by providing an easier way to include supporting libraries, use debugging tools, and get error messages when issues arise during development. However, it does not provide any benefits or changes to the user interface.

## Code Review Recommendation
I would recommend that the commit be code reviewed since it involves making changes to the Visual Studio project template. The author has followed best practices by using the latest C++ compiler and tools such as Clang and Intellisense. They have also included detailed documentation on how to build, compile, link, and run projects with VS2015. I would recommend that reviewers focus on verifying that all code compiles correctly, links without errors, and runs smoothly under the new project template before recommending it for inclusion in the repository.

## Documentation Impact
The commit has no significant impact on documentation since it is primarily focused on improving how to build and debug projects with Visual Studio 2015 using a new project template. The author has included detailed explanations of the changes made, including any new functionality or libraries that were added. They have also included example code in some cases to demonstrate how to use certain features or APIs. While some documentation may need to be updated, it is not likely to require significant revisions given the minimal changes involved.