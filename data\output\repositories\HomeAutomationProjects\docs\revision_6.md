## Summary
### Changes
* `RELAY_PIN` changed from `4` to `12` in the code and a new file (`relay.h`) added (contains LED pin assignment)
* `SWITCH_PIN` changed from `4` to `4` in the code

### Technical Details
The changes made are mostly related to improving code quality, readability, and organization by adding comments, updating variable names for clarity, and organizing the header file (`relay.h`) accordingly.

### Impact Assessment
* **Code Review Recommendation:** The commit is relatively minor and does not pose a high risk of introducing bugs or affecting other parts of the system. It should be reviewed but will have minimal impact on existing code quality.
* **Documentation Impact:** The changes do not directly affect documentation, as it is not user-facing and concerns configuration options and API endpoints. No additional documentation needs to be updated or created for these changes.
* **Security Implications:** There are no apparent security implications from these changes.
* **Users:** The changes are primarily internal to the system (i.e., they do not affect end-users directly) but may have indirect effects on how users interact with the device.

## Code Review Recommendation
Since the changes are minor and pose minimal risk, a code review is recommended for these commits as it can help maintain consistency in coding standards and ensure that new features align with established best practices. However, considering the low impact of these changes, they should not be considered high priority or critical issues to address immediately.

## Recommendations
* No additional follow-up actions are required at this time.
* Keep an eye on future commits to ensure that any further revisions maintain consistent coding standards and align with established best practices.