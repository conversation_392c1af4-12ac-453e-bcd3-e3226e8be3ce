Summary: This commit introduces a new feature to the arduino16x project by adding support for the esp8266 microcontroller. The features include flashing an LED and logging data to the serial console, along with supporting libraries for ESP32 and ESP01 development boards. Visual Micro is used to configure the system's serial port settings.

Technical Details:
- The commit adds two new components to the arduino16x project: a library for reading from/writing to a serial console (serialconsole) and a class for flashing an LED on an ESP8266 module (LED). Both libraries are part of the Visual Micro framework.
- The ESP32 and ESP01 development boards are supported through visual micro plugins.
- To configure the system's serial port settings, the Visual Studio project is modified to use the Visual Micro IDE instead of the default Arduino IDE.
- No changes have been made to the existing codebase or system functionality.

Impact Assessment:
- This commit has no impact on users since it does not affect any user-facing features or functionality of the arduino16x project.
- The commit affects developers who are working with the Visual Micro framework and need support for ESP8266 development boards.
- No new bugs have been introduced in this commit, as all required libraries are compatible with the existing codebase.
- The commit does not affect system security since no changes to configuration files or API endpoints have been made.

Code Review Recommendation:
- This commit should be reviewed to ensure that it follows best practices for using Visual Micro and properly configures serial console settings. Additionally, any new libraries should be thoroughly tested to ensure compatibility with the existing codebase.
- The changes in this commit are minor and focused on adding support for ESP8266 development boards through the Visual Micro framework, which is generally considered a low-risk change that will not affect users or system functionality.

Documentation Impact:
- This commit does not affect documentation since no new features have been added to the arduino16x project's user interface or API endpoints. However, if new libraries are added in future commits, any changes made to them could impact documentation related to those libraries.

Recommendations:
- Consider adding a note to the README file about the support for ESP8266 development boards through Visual Micro and providing instructions on how to use it.
- Update setup guides to reflect the new libraries that have been added to the project.