{"data_mtime": 1754614259, "dep_lines": [8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 109, 110, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlite3", "json", "logging", "threading", "pathlib", "datetime", "typing", "dataclasses", "contextlib", "database_migration", "os", "stat", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc", "json.decoder", "json.encoder", "types", "typing_extensions"], "hash": "594fc2c5688b2234acc09d563e4f1d30fb0c3431", "id": "document_database", "ignore_all": false, "interface_hash": "4ca5177e1586539f41a0c78c8279f44789744635", "mtime": 1754614308, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\document_database.py", "plugin_data": null, "size": 26195, "suppressed": [], "version_id": "1.15.0"}